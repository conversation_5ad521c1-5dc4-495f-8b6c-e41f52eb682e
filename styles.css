/* 🌟 NEON CRYPTO WHALE TRACKER - ULTRA GÖSTERIŞLI CSS 🌟 */

/* Reset ve Te<PERSON> Stiller */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 🌟 NEON KEYFRAME ANIMASYONLARI */
@keyframes neonPulse {
    0%, 100% {
        text-shadow: var(--neon-glow-cyan);
        box-shadow: var(--neon-glow-cyan);
    }
    50% {
        text-shadow: 0 0 30px #00ffff, 0 0 60px #00ffff, 0 0 90px #00ffff;
        box-shadow: 0 0 30px #00ffff, 0 0 60px #00ffff, 0 0 90px #00ffff;
    }
}

@keyframes neonFlicker {
    0%, 100% { opacity: 1; }
    2% { opacity: 0.8; }
    4% { opacity: 1; }
    8% { opacity: 0.9; }
    10% { opacity: 1; }
    12% { opacity: 0.7; }
    14% { opacity: 1; }
}

@keyframes neonRainbow {
    0% {
        color: #00ffff;
        text-shadow: var(--neon-glow-cyan);
    }
    25% {
        color: #ff00ff;
        text-shadow: var(--neon-glow-pink);
    }
    50% {
        color: #ffff00;
        text-shadow: var(--neon-glow-yellow);
    }
    75% {
        color: #00ff00;
        text-shadow: var(--neon-glow-green);
    }
    100% {
        color: #00ffff;
        text-shadow: var(--neon-glow-cyan);
    }
}

@keyframes neonBorder {
    0% { border-color: #00ffff; box-shadow: var(--neon-glow-cyan); }
    25% { border-color: #ff00ff; box-shadow: var(--neon-glow-pink); }
    50% { border-color: #ffff00; box-shadow: var(--neon-glow-yellow); }
    75% { border-color: #00ff00; box-shadow: var(--neon-glow-green); }
    100% { border-color: #00ffff; box-shadow: var(--neon-glow-cyan); }
}

@keyframes neonFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(-1deg); }
    75% { transform: translateY(-15px) rotate(0.5deg); }
}

@keyframes neonZoom {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes neonSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes neonWave {
    0%, 100% { transform: scaleX(1); }
    50% { transform: scaleX(1.1); }
}

@keyframes neonGradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes loadingPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.02);
    }
}

@keyframes loadingShimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes spinnerRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

:root {
    /* 🌟 NEON CRYPTO THEME - ULTRA GÖSTERIŞLI */
    --bg-primary: #000000;
    --bg-secondary: #0a0a0a;
    --bg-tertiary: #111111;
    --text-primary: #ffffff;
    --text-secondary: #00ffff;
    --accent-primary: #00ffff;
    --accent-secondary: #ff00ff;
    --accent-tertiary: #ffff00;
    --success: #00ff00;
    --danger: #ff0040;
    --warning: #ffff00;
    --card-bg: rgba(0, 255, 255, 0.03);
    --border-color: rgba(0, 255, 255, 0.3);
    --shadow: rgba(0, 255, 255, 0.5);

    /* 🌈 Neon Gölgeler */
    --neon-glow-cyan: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
    --neon-glow-pink: 0 0 20px #ff00ff, 0 0 40px #ff00ff, 0 0 60px #ff00ff;
    --neon-glow-yellow: 0 0 20px #ffff00, 0 0 40px #ffff00, 0 0 60px #ffff00;
    --neon-glow-green: 0 0 20px #00ff00, 0 0 40px #00ff00, 0 0 60px #00ff00;
    --neon-glow-red: 0 0 20px #ff0040, 0 0 40px #ff0040, 0 0 60px #ff0040;
}

/* Light theme kaldırıldı - Sadece neon tema */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    transition: all 0.3s ease;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 🇹🇷 TÜRK BAYRAĞI STİLLERİ */
.turkish-flag {
    position: relative;
    width: 60px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 0 25px rgba(227, 10, 23, 0.8);
    animation: flagWave 2.5s ease-in-out infinite;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform-style: preserve-3d;
}

.flag-background {
    width: 100%;
    height: 100%;
    background: #e30a17;
    position: relative;
    animation: flagRipple 2s ease-in-out infinite;
}

.crescent {
    position: absolute;
    width: 14px;
    height: 14px;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    animation: crescentFloat 3s ease-in-out infinite;
}

.crescent::before {
    content: '';
    position: absolute;
    width: 14px;
    height: 14px;
    background: white;
    border-radius: 50%;
    left: 0;
    top: 0;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.crescent::after {
    content: '';
    position: absolute;
    width: 11px;
    height: 11px;
    background: #e30a17;
    border-radius: 50%;
    left: 4px;
    top: 1.5px;
}

.star {
    position: absolute;
    left: 35px;
    top: 50%;
    transform: translateY(-50%) rotate(0deg);
    color: white;
    font-size: 10px;
    animation: starTwinkle 2s ease-in-out infinite, starFloat 3.5s ease-in-out infinite;
}

.star::before {
    content: '★';
    text-shadow: 0 0 5px rgba(255, 255, 255, 1), 0 0 10px rgba(255, 255, 255, 0.8);
    filter: drop-shadow(0 0 2px white);
}

@keyframes starTwinkle {
    0%, 100% {
        opacity: 1;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 0.8;
        transform: translateY(-50%) scale(1.1);
    }
}

@keyframes flagWave {
    0%, 100% {
        transform: perspective(150px) rotateY(0deg) rotateX(0deg) rotateZ(0deg);
        box-shadow: 0 0 25px rgba(227, 10, 23, 0.8);
    }
    20% {
        transform: perspective(150px) rotateY(-8deg) rotateX(3deg) rotateZ(-1deg);
        box-shadow: 0 0 30px rgba(227, 10, 23, 0.9);
    }
    40% {
        transform: perspective(150px) rotateY(0deg) rotateX(-3deg) rotateZ(1deg);
        box-shadow: 0 0 35px rgba(227, 10, 23, 1);
    }
    60% {
        transform: perspective(150px) rotateY(8deg) rotateX(3deg) rotateZ(-1deg);
        box-shadow: 0 0 30px rgba(227, 10, 23, 0.9);
    }
    80% {
        transform: perspective(150px) rotateY(0deg) rotateX(-2deg) rotateZ(0.5deg);
        box-shadow: 0 0 28px rgba(227, 10, 23, 0.85);
    }
}

@keyframes flagRipple {
    0%, 100% {
        transform: scaleX(1) scaleY(1) skewX(0deg);
    }
    25% {
        transform: scaleX(1.03) scaleY(0.97) skewX(1deg);
    }
    50% {
        transform: scaleX(0.98) scaleY(1.02) skewX(-1deg);
    }
    75% {
        transform: scaleX(1.02) scaleY(0.99) skewX(0.5deg);
    }
}

@keyframes crescentFloat {
    0%, 100% {
        transform: translateY(-50%) translateX(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-52%) translateX(1px) rotate(2deg);
    }
    66% {
        transform: translateY(-48%) translateX(-1px) rotate(-2deg);
    }
}

@keyframes starFloat {
    0%, 100% {
        transform: translateY(-50%) translateX(0px) rotate(0deg) scale(1);
    }
    25% {
        transform: translateY(-52%) translateX(1px) rotate(5deg) scale(1.05);
    }
    50% {
        transform: translateY(-48%) translateX(-1px) rotate(-5deg) scale(0.95);
    }
    75% {
        transform: translateY(-51%) translateX(0.5px) rotate(3deg) scale(1.02);
    }
}

.logo i {
    font-size: 2rem;
    color: #00ffff;
    text-shadow: var(--neon-glow-cyan);
    animation: neonPulse 3s infinite, neonSpin 10s linear infinite;
}

/* 🇹🇷 TÜRK BAYRAĞI YAZISI STİLLERİ */
.turkish-flag-text {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    gap: 8px;
    animation: textWave 4s ease-in-out infinite;
}

.flag-text-red {
    color: #e30a17;
    text-shadow:
        0 0 10px rgba(227, 10, 23, 0.8),
        0 0 20px rgba(227, 10, 23, 0.6),
        0 0 30px rgba(227, 10, 23, 0.4);
    animation: redTextGlow 2s ease-in-out infinite alternate;
}

.flag-text-white {
    color: #ffffff;
    text-shadow:
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(255, 255, 255, 0.6),
        0 0 30px rgba(255, 255, 255, 0.4);
    animation: whiteTextGlow 2s ease-in-out infinite alternate;
}

@keyframes textWave {
    0%, 100% {
        transform: translateY(0px);
    }
    25% {
        transform: translateY(-2px);
    }
    50% {
        transform: translateY(0px);
    }
    75% {
        transform: translateY(2px);
    }
}

@keyframes redTextGlow {
    0% {
        text-shadow:
            0 0 10px rgba(227, 10, 23, 0.8),
            0 0 20px rgba(227, 10, 23, 0.6),
            0 0 30px rgba(227, 10, 23, 0.4);
    }
    100% {
        text-shadow:
            0 0 15px rgba(227, 10, 23, 1),
            0 0 25px rgba(227, 10, 23, 0.8),
            0 0 35px rgba(227, 10, 23, 0.6);
    }
}

@keyframes whiteTextGlow {
    0% {
        text-shadow:
            0 0 10px rgba(255, 255, 255, 0.8),
            0 0 20px rgba(255, 255, 255, 0.6),
            0 0 30px rgba(255, 255, 255, 0.4);
    }
    100% {
        text-shadow:
            0 0 15px rgba(255, 255, 255, 1),
            0 0 25px rgba(255, 255, 255, 0.8),
            0 0 35px rgba(255, 255, 255, 0.6);
    }
}

/* Kelime bazında dalgalanma efekti */
.turkish-flag-text span:nth-child(1) {
    animation-delay: 0s;
}

.turkish-flag-text span:nth-child(2) {
    animation-delay: 0.3s;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid rgba(0, 255, 0, 0.3);
    animation: pulse-green 2s infinite;
}

.live-indicator i {
    font-size: 0.8rem !important;
    color: #00ff00 !important;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(0, 255, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 255, 0, 0); }
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(0, 212, 255, 0.2);
    color: var(--accent-primary);
}

/* Theme Toggle & Fullscreen Buttons */
.theme-toggle,
.fullscreen-toggle {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 0.5rem;
}

.theme-toggle:hover,
.fullscreen-toggle:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    transform: translateY(-2px);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

/* İstatistik Kartları */
.stats-section {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(0, 255, 255, 0.03);
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    animation: neonFloat 6s ease-in-out infinite;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00, #00ffff);
    background-size: 400% 400%;
    border-radius: 16px;
    z-index: -1;
    animation: neonGradientShift 4s ease infinite;
    opacity: 0.3;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: #00ffff;
    box-shadow: var(--neon-glow-cyan);
    animation: neonZoom 0.5s ease, neonFloat 6s ease-in-out infinite;
}

.stat-card:nth-child(2n) {
    animation-delay: -2s;
}

.stat-card:nth-child(3n) {
    animation-delay: -4s;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ffff);
    background-size: 300% 300%;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    text-shadow: var(--neon-glow-cyan);
    box-shadow: var(--neon-glow-cyan);
    animation: neonGradientShift 3s ease-in-out infinite, neonPulse 2s infinite;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: neonWave 2s infinite;
}

.stat-content h3 {
    font-size: 0.9rem;
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
}

.stat-change.positive {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.stat-change.negative {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

/* Filtreler */
.filters-section {
    margin-bottom: 3rem;
}

.filters {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.9rem;
    color: #cccccc;
}

.filter-group select,
.filter-group input {
    padding: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    color: white;
    min-width: 150px;
}

.filter-btn {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Grafik */
.chart-section {
    margin-bottom: 3rem;
}

.chart-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
}

.chart-container h2 {
    margin-bottom: 1rem;
    color: #00d4ff;
}

/* Tablo */
.transactions-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #00ff00;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.table-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
}

.whale-table {
    width: 100%;
    border-collapse: collapse;
}

.whale-table th {
    background: rgba(0, 212, 255, 0.1);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #00d4ff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.whale-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.whale-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.transaction-type {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.transaction-type.buy {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.transaction-type.sell {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

.transaction-status {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

.transaction-status.confirmed {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.transaction-status.pending {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

/* Piyasa Analizi */
.market-analysis-section {
    margin-bottom: 3rem;
}

.analysis-header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.analysis-header-section h2 {
    margin: 0;
}

.analysis-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: end;
}

.coin-selector,
.timeframe-selector,
.analysis-mode {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.coin-selector label,
.timeframe-selector label,
.analysis-mode label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.coin-selector select,
.timeframe-selector select,
.analysis-mode select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--card-bg);
    color: var(--text-primary);
    min-width: 150px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.coin-selector select:focus,
.timeframe-selector select:focus,
.analysis-mode select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 10px var(--shadow);
}

.coin-selector select:hover,
.timeframe-selector select:hover,
.analysis-mode select:hover {
    border-color: var(--accent-primary);
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analysis-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.analysis-card:hover {
    transform: translateY(-5px);
    border-color: rgba(0, 212, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.1);
}

.analysis-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.analysis-header i {
    color: #00d4ff;
    font-size: 1.2rem;
}

/* Güvenilirlik Skoru Kartı */
.reliability-score {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 153, 204, 0.05) 100%);
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.reliability-display {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.reliability-meter {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.reliability-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--danger) 0%, var(--warning) 50%, var(--success) 100%);
    border-radius: 4px;
    transition: width 0.8s ease;
    position: relative;
}

.reliability-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.reliability-value {
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
}

.reliability-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.4;
}

/* Trend Güveni Kartı */
.trend-confidence {
    background: linear-gradient(135deg, rgba(255, 165, 0, 0.1) 0%, rgba(255, 140, 0, 0.05) 100%);
    border: 1px solid rgba(255, 165, 0, 0.2);
}

.trend-display {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.trend-strength {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
    position: relative;
}

.trend-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--danger) 0%, var(--warning) 50%, var(--success) 100%);
    border-radius: 6px;
    transition: width 0.8s ease;
    position: relative;
}

.trend-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
    animation: trend-pulse 3s infinite;
}

@keyframes trend-pulse {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.trend-value {
    font-size: 1.3rem;
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
}

.trend-recommendation {
    font-size: 0.85rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.4;
    font-style: italic;
}

/* Zaman aralığı uyarı stilleri */
.timeframe-selector select option.short-term {
    background: rgba(255, 165, 0, 0.2);
    color: var(--warning);
}

/* Gelişmiş animasyonlar */
.analysis-card.reliability-score:hover {
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.2);
    transform: translateY(-8px) scale(1.02);
}

.analysis-card.trend-confidence:hover {
    box-shadow: 0 15px 40px rgba(255, 165, 0, 0.2);
    transform: translateY(-8px) scale(1.02);
}

/* Detaylı analiz bölümü iyileştirmeleri */
.detailed-analysis {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 2rem;
    margin-top: 2rem;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.detailed-analysis::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));
    animation: gradient-flow 3s ease-in-out infinite;
}

@keyframes gradient-flow {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.detailed-analysis h3 {
    color: var(--accent-primary);
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.analysis-text {
    line-height: 1.8;
    font-size: 0.95rem;
}

.analysis-text strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Dropdown option stilleri - Daha güçlü kurallar */
select option {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
    padding: 8px 12px !important;
}

.analysis-mode select option,
.coin-selector select option,
.timeframe-selector select option {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
    padding: 8px 12px !important;
    border: none !important;
}

.analysis-mode select option:hover,
.coin-selector select option:hover,
.timeframe-selector select option:hover {
    background-color: rgba(0, 212, 255, 0.1) !important;
    color: var(--accent-primary) !important;
}

/* Seçili option */
.analysis-mode select option:checked,
.coin-selector select option:checked,
.timeframe-selector select option:checked {
    background-color: var(--accent-primary) !important;
    color: var(--bg-primary) !important;
}

/* Select elementleri için genel stil */
.analysis-controls select {
    background-color: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 0.9rem;
    outline: none;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300d4ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 40px;
}

.analysis-controls select:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.2);
}

.analysis-controls select:focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

/* Tarayıcı özel dropdown stilleri */
select::-webkit-scrollbar {
    width: 8px;
}

select::-webkit-scrollbar-track {
    background: #1a1a1a;
}

select::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 4px;
}

/* Firefox için */
@-moz-document url-prefix() {
    select option {
        background-color: #1a1a1a !important;
        color: #ffffff !important;
    }
}

/* Chrome/Safari için özel stil */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select option {
        background-color: #1a1a1a;
        color: #ffffff;
    }

    select option:checked {
        background-color: var(--accent-primary);
        color: #000000;
    }
}

/* Responsive iyileştirmeler */
@media (max-width: 768px) {
    .analysis-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .coin-selector select,
    .timeframe-selector select,
    .analysis-mode select {
        min-width: 100%;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .reliability-value,
    .trend-value {
        font-size: 1.2rem;
    }
}

.analysis-header h3 {
    font-size: 1rem;
    color: #ffffff;
}

/* Sentiment Indicator */
.sentiment-indicator {
    text-align: center;
}

.sentiment-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.sentiment-value.bullish {
    color: #00ff00;
}

.sentiment-value.bearish {
    color: #ff4444;
}

.sentiment-value.neutral {
    color: #ffa500;
}

.sentiment-description {
    font-size: 0.9rem;
    color: #cccccc;
}

/* Buy/Sell Ratio */
.ratio-bars {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.buy-bar, .sell-bar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.bar-fill {
    flex: 1;
    height: 8px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.buy-fill {
    background: linear-gradient(90deg, #00ff00, #00cc00);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.sell-fill {
    background: linear-gradient(90deg, #ff4444, #cc0000);
    border-radius: 4px;
    transition: width 0.5s ease;
}

/* Price Prediction */
.prediction-display {
    text-align: center;
}

.prediction-direction {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.prediction-direction.up {
    color: #00ff00;
}

.prediction-direction.down {
    color: #ff4444;
}

.prediction-direction.sideways {
    color: #ffa500;
}

.prediction-confidence {
    font-size: 0.9rem;
    color: #cccccc;
}

/* Top Activity */
.activity-display {
    text-align: center;
}

.coin-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #00d4ff;
    margin-bottom: 0.5rem;
}

.activity-count {
    font-size: 1rem;
    color: #ffffff;
    margin-bottom: 0.3rem;
}

.activity-trend {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Whale Strength */
.strength-display {
    text-align: center;
}

.strength-meter {
    width: 100%;
    height: 20px;
    background: var(--border-color);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    margin-bottom: 1rem;
}

.strength-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--danger), var(--warning), var(--success));
    border-radius: 10px;
    transition: width 0.5s ease;
    width: 50%;
}

.strength-value {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.strength-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Market Momentum */
.momentum-display {
    text-align: center;
}

.momentum-arrow {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.momentum-arrow.up {
    color: var(--success);
    transform: rotate(-45deg);
}

.momentum-arrow.down {
    color: var(--danger);
    transform: rotate(45deg);
}

.momentum-arrow.neutral {
    color: var(--warning);
    transform: rotate(0deg);
}

.momentum-value {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.momentum-score {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Volume Analysis */
.volume-comparison {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.volume-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--border-color);
    border-radius: 6px;
}

.volume-item span:first-child {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.volume-item span:last-child {
    color: var(--text-primary);
    font-weight: bold;
}

/* Fear & Greed Index */
.fear-greed-display {
    text-align: center;
}

.fear-greed-meter {
    position: relative;
    width: 120px;
    height: 60px;
    margin: 0 auto 1rem;
    background: linear-gradient(90deg, var(--danger) 0%, var(--warning) 50%, var(--success) 100%);
    border-radius: 60px 60px 0 0;
    overflow: hidden;
}

.fear-greed-needle {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 2px;
    height: 50px;
    background: var(--text-primary);
    transform-origin: bottom center;
    transform: translateX(-50%) rotate(0deg);
    transition: transform 0.5s ease;
}

.fear-greed-needle::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -3px;
    width: 8px;
    height: 8px;
    background: var(--text-primary);
    border-radius: 50%;
}

.fear-greed-scale {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.fear-greed-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-primary);
}

/* Detailed Analysis */
.detailed-analysis {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

.detailed-analysis h3 {
    color: #00d4ff;
    margin-bottom: 1rem;
}

.analysis-text {
    line-height: 1.6;
    color: #ffffff;
    font-size: 0.95rem;
}

/* Uyarılar */
.alerts-section {
    margin-bottom: 3rem;
}

.alerts-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert.high {
    border-color: rgba(255, 0, 0, 0.3);
    background: rgba(255, 0, 0, 0.1);
}

.alert.medium {
    border-color: rgba(255, 165, 0, 0.3);
    background: rgba(255, 165, 0, 0.1);
}

.alert-icon {
    font-size: 1.5rem;
}

.alert.high .alert-icon {
    color: #ff4444;
}

.alert.medium .alert-icon {
    color: #ffa500;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    text-align: center;
    color: #cccccc;
}

/* Portföy Bölümü */
.portfolio-section {
    margin-bottom: 3rem;
}

.portfolio-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.portfolio-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.portfolio-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
}

.portfolio-value,
.portfolio-pnl,
.best-performer {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.portfolio-change,
.pnl-percentage,
.performer-change {
    font-size: 0.9rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
}

.portfolio-change.positive,
.pnl-percentage.positive,
.performer-change.positive {
    background: rgba(0, 255, 0, 0.2);
    color: var(--success);
}

.portfolio-change.negative,
.pnl-percentage.negative,
.performer-change.negative {
    background: rgba(255, 0, 0, 0.2);
    color: var(--danger);
}

.add-coin-btn {
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-coin-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow);
}

/* Portfolio Table */
.portfolio-table-container {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
}

.portfolio-table {
    width: 100%;
    border-collapse: collapse;
}

.portfolio-table th,
.portfolio-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.portfolio-table th {
    background: rgba(0, 212, 255, 0.1);
    color: var(--accent-primary);
    font-weight: 600;
}

.portfolio-table tr:hover {
    background: var(--card-bg);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-secondary);
    margin: 5% auto;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--accent-primary);
}

.close {
    color: var(--text-secondary);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: var(--danger);
}

.modal-body {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--card-bg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 10px var(--shadow);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.form-actions button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-actions button[type="submit"] {
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    color: white;
}

.form-actions button[type="button"] {
    background: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.form-actions button:hover {
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .whale-table {
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .analysis-header-section {
        flex-direction: column;
        align-items: flex-start;
    }

    .nav {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .theme-toggle,
    .fullscreen-toggle {
        margin-left: 0;
    }
}

/* Teknik Analiz */
.technical-analysis-section {
    margin-bottom: 3rem;
}

.technical-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.technical-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.technical-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
}

.technical-card h3 {
    color: var(--accent-primary);
    margin-bottom: 1rem;
}

/* RSI Indicator */
.rsi-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rsi-bar {
    flex: 1;
    height: 20px;
    background: var(--border-color);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.rsi-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success), var(--warning), var(--danger));
    border-radius: 10px;
    transition: width 0.5s ease;
    width: 50%;
}

.rsi-value {
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--text-primary);
}

/* MACD Display */
.macd-display div {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.macd-display span {
    color: var(--text-primary);
    font-weight: bold;
}

/* Bollinger Bands */
.bollinger-display div {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.bollinger-display span {
    color: var(--text-primary);
    font-weight: bold;
}

/* Support/Resistance */
.sr-levels div {
    margin-bottom: 0.5rem;
}

.resistance {
    color: var(--danger);
}

.support {
    color: var(--success);
}

.sr-levels span {
    font-weight: bold;
}

/* Reports Section */
.reports-section {
    margin-bottom: 3rem;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.report-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow);
}

.report-card h3 {
    color: var(--accent-primary);
    margin-bottom: 0.5rem;
}

.report-card p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.report-card button {
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.report-card button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow);
}

/* PWA Install Banner */
.install-banner {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: var(--bg-secondary);
    border: 1px solid var(--accent-primary);
    border-radius: 12px;
    padding: 1rem;
    z-index: 1000;
    box-shadow: 0 10px 30px var(--shadow);
    animation: slideUp 0.5s ease;
}

@keyframes slideUp {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.install-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.install-content i {
    font-size: 2rem;
    color: var(--accent-primary);
}

.install-content div {
    flex: 1;
}

.install-content h4 {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
}

.install-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.install-content button {
    background: var(--accent-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    margin-left: 0.5rem;
    transition: all 0.3s ease;
}

.install-content button:hover {
    background: var(--accent-secondary);
}

.install-content button:last-child {
    background: transparent;
    color: var(--text-secondary);
    font-size: 1.5rem;
    padding: 0.25rem 0.5rem;
}

/* Fullscreen Mode */
.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: var(--bg-primary);
    overflow: auto;
}

.fullscreen-mode .header {
    position: sticky;
    top: 0;
    z-index: 10000;
}

/* Section Navigation */
.section-hidden {
    display: none !important;
}

.section-visible {
    display: block !important;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 🎯 MOD KONTROL STİLLERİ */
.mode-info {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-left: 1rem;
}

.mode-help-btn,
.mode-compare-btn,
.mode-history-btn {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: #00ffff;
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.mode-help-btn:hover,
.mode-compare-btn:hover,
.mode-history-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: var(--neon-glow-cyan);
    transform: translateY(-2px);
}

/* 🎯 MOD MODAL STİLLERİ */
.mode-help-content,
.mode-comparison-content,
.mode-history-content {
    max-width: 900px;
    max-height: 80vh;
    overflow-y: auto;
}

.mode-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.mode-help-item {
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.mode-help-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.mode-help-item:hover::before {
    left: 100%;
}

.mode-help-item:hover {
    border-color: #00ffff;
    box-shadow: var(--neon-glow-cyan);
    transform: translateY(-5px);
}

.mode-icon {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 1rem;
    animation: neonPulse 3s infinite;
}

.mode-help-item h4 {
    color: #00ffff;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.mode-help-item p {
    color: #cccccc;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.mode-help-item ul {
    list-style: none;
    padding: 0;
}

.mode-help-item li {
    color: #aaaaaa;
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    padding-left: 1rem;
    position: relative;
}

.mode-help-item li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #00ff00;
    font-weight: bold;
}

/* 🎯 MOD KARŞILAŞTIRMA STİLLERİ */
.comparison-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.comparison-controls select {
    padding: 0.5rem;
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    min-width: 150px;
}

.vs-text {
    font-weight: bold;
    color: #ff00ff;
    font-size: 1.2rem;
    text-shadow: var(--neon-glow-pink);
}

.comparison-controls button {
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.comparison-controls button:hover {
    transform: translateY(-2px);
    box-shadow: var(--neon-glow-cyan);
}

.comparison-results {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 200px;
}

/* 🎯 MOD GEÇMİŞİ STİLLERİ */
.history-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-item span:first-child {
    color: #cccccc;
    font-size: 0.9rem;
}

.stat-item span:last-child {
    color: #00ffff;
    font-weight: bold;
}

.history-timeline {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    min-height: 300px;
}

/* 🎯 ANALİZ MODU TEMA DEĞİŞİKLİKLERİ */
.analysis-mode-quick .analysis-card {
    border-color: #ffff00;
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.3);
}

.analysis-mode-ai .analysis-card {
    border-color: #ff00ff;
    box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
}

.analysis-mode-research .analysis-card {
    border-color: #00ff00;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
}

.analysis-mode-technical .analysis-card {
    border-color: #ff8800;
    box-shadow: 0 0 20px rgba(255, 136, 0, 0.3);
}

.analysis-mode-professional .analysis-card {
    border-color: #8800ff;
    box-shadow: 0 0 20px rgba(136, 0, 255, 0.3);
}

/* Responsive Design Updates */
@media (max-width: 768px) {
    .mode-info {
        margin-left: 0;
        margin-top: 0.5rem;
    }

    .mode-help-grid {
        grid-template-columns: 1fr;
    }

    .comparison-controls {
        flex-direction: column;
        gap: 0.5rem;
    }

    .comparison-controls select {
        min-width: 100%;
    }
}

/* 🎯 KARŞILAŞTIRMA SONUÇLARI STİLLERİ */
.comparison-result h4 {
    color: #00ffff;
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.comparison-metrics {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.metric {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.metric span {
    min-width: 80px;
    color: #cccccc;
    font-weight: bold;
}

.metric-bars {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.metric-bar {
    height: 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
    transition: all 0.3s ease;
}

.metric-bar:hover {
    transform: scaleX(1.02);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.comparison-recommendation {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.comparison-recommendation p {
    margin: 0;
    color: #00ffff;
}

/* 🎯 TIMELINE STİLLERİ */
.timeline-items {
    max-height: 250px;
    overflow-y: auto;
}

.timeline-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.timeline-item:hover {
    background: rgba(0, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.3);
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-time {
    min-width: 120px;
    font-size: 0.8rem;
    color: #888888;
}

.timeline-content {
    flex: 1;
}

.timeline-content strong {
    color: #00ffff;
}

.timeline-details {
    color: #cccccc;
    font-size: 0.9rem;
}

/* 🎯 KLAVYE KISAYOLLARI */
.keyboard-shortcuts {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.8rem;
    color: #cccccc;
    z-index: 1000;
    display: none;
}

.keyboard-shortcuts.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

.keyboard-shortcuts h4 {
    color: #00ffff;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.keyboard-shortcuts ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.keyboard-shortcuts li {
    margin-bottom: 0.3rem;
}

.keyboard-shortcuts kbd {
    background: rgba(0, 255, 255, 0.2);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 3px;
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
    color: #00ffff;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 🎯 BİLDİRİM STİLLERİ */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(0, 255, 255, 0.5);
    border-radius: 8px;
    padding: 1rem;
    color: white;
    z-index: 10000;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: var(--neon-glow-cyan);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.notification-content button {
    background: none;
    border: none;
    color: #ff4444;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-content button:hover {
    color: #ff6666;
}

.notification-info {
    border-color: rgba(0, 255, 255, 0.5);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.notification-success {
    border-color: rgba(0, 255, 0, 0.5);
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
}

.notification-warning {
    border-color: rgba(255, 255, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.3);
}

.notification-error {
    border-color: rgba(255, 0, 0, 0.5);
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);
}

/* 🎯 LOADING ANİMASYONLARI */
.analysis-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 16px;
    padding: 2rem;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: var(--neon-glow-cyan);
    animation: neonPulse 2s infinite;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--text-primary);
}

.loading-spinner i {
    font-size: 2rem;
    color: #00ffff;
    animation: spinnerRotate 1s linear infinite;
    text-shadow: var(--neon-glow-cyan);
}

.loading-spinner span {
    font-size: 1rem;
    text-align: center;
    color: #00ffff;
    text-shadow: var(--neon-glow-cyan);
    animation: neonFlicker 2s infinite;
}

/* Analiz kartları loading durumu */
.analysis-card.loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
    animation: loadingPulse 1.5s infinite;
}

.analysis-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(0, 255, 255, 0.1),
        transparent
    );
    animation: loadingShimmer 1.5s infinite;
    border-radius: inherit;
}

/* 🎯 LAZY LOADING ANİMASYONLARI */
.analysis-card {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.analysis-card.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 🎯 GELIŞMIŞ HOVER EFEKTLERİ */
.analysis-card:hover {
    transform: translateY(-5px) scale(1.02);
    z-index: 10;
}

.analysis-card:hover .analysis-header i {
    animation: neonSpin 2s linear infinite;
}

/* 🎯 MOD DEĞİŞİKLİK ANİMASYONLARI */
.mode-transition {
    animation: modeTransition 0.8s ease;
}

@keyframes modeTransition {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(0.95);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0);
    }
}

/* 🎯 RESPONSIVE İYİLEŞTİRMELERİ */
@media (max-width: 768px) {
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }

    .notification.show {
        transform: translateY(0);
    }

    .keyboard-shortcuts {
        bottom: 10px;
        right: 10px;
        left: 10px;
        font-size: 0.7rem;
    }

    .analysis-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .analysis-controls > div {
        width: 100%;
    }

    /* 🇹🇷 Mobil için Türk Bayrağı Düzenlemeleri */
    .logo {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .turkish-flag {
        width: 50px;
        height: 33px;
    }

    .turkish-flag-text {
        font-size: 1.2rem;
        gap: 6px;
        text-align: center;
        justify-content: center;
    }
}
